<template>
  <div class="vue2-datatable-test">
    <h1>Vue2 DataTable Virtual Scrolling Test</h1>
    <p>Testing virtual scrolling with large datasets to ensure all records are visible during scroll operations.</p>
    
    <div class="test-controls">
      <div class="control-group">
        <label>
          <input v-model="enableVirtualScrolling" type="checkbox" />
          Enable Virtual Scrolling
        </label>
        <label>
          <input v-model="enablePerformanceMonitoring" type="checkbox" />
          Performance Monitoring
        </label>
      </div>
      
      <div class="control-group">
        <label>
          Data Size:
          <select v-model="dataSize" @change="generateData">
            <option value="small">Small (100 rows)</option>
            <option value="medium">Medium (1,000 rows)</option>
            <option value="large">Large (10,000 rows)</option>
            <option value="xlarge">X-Large (50,000 rows)</option>
          </select>
        </label>
      </div>
    </div>

    <!-- Performance Stats -->
    <div v-if="enablePerformanceMonitoring" class="performance-stats">
      <div class="stat-item">
        <span class="stat-label">Render Time:</span>
        <span class="stat-value">{{ performanceStats.renderTime }}ms</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Memory Usage:</span>
        <span class="stat-value">{{ performanceStats.memoryUsage }}MB</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Items Count:</span>
        <span class="stat-value">{{ performanceStats.itemCount }}</span>
      </div>
    </div>

    <!-- Vue2 DataTable -->
    <Vue2DataTable
      :columns="testColumns"
      :data-source="testData"
      :virtual-scroll-enabled="enableVirtualScrolling"
      :enable-performance-monitoring="enablePerformanceMonitoring"
      :show-search="true"
      :show-pagination="false"
      :show-total-bar="true"
      :selectable="true"
      :select-on-row-click="true"
      :multiple-selection="true"
      :striped="true"
      :hover="true"
      :row-height="50"
      search-placeholder="Search test data..."
      @performance-update="handlePerformanceUpdate"
      @row-click="handleRowClick"
      @scroll="handleScroll"
    />

    <!-- Event Log -->
    <div class="event-log">
      <h3>Event Log</h3>
      <div class="log-entries">
        <div
          v-for="(event, index) in eventLog"
          :key="index"
          class="log-entry"
        >
          <span class="log-time">{{ event.time }}</span>
          <span class="log-type">{{ event.type }}</span>
          <span class="log-data">{{ event.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue2DataTable from '@/components/common/Vue2DataTable'

export default {
  name: 'Vue2DataTableTest',
  
  components: {
    Vue2DataTable
  },

  data() {
    return {
      // Test controls
      enableVirtualScrolling: true,
      enablePerformanceMonitoring: true,
      dataSize: 'medium',
      
      // Performance stats
      performanceStats: {
        renderTime: 0,
        memoryUsage: 0,
        itemCount: 0
      },
      
      // Event log
      eventLog: [],
      maxLogEntries: 50,
      
      // Test data
      testData: [],
      testColumns: [
        {
          key: 'id',
          label: 'ID',
          sortable: true,
          searchable: true,
          width: '80px',
          type: 'number'
        },
        {
          key: 'name',
          label: 'Name',
          sortable: true,
          searchable: true,
          width: '200px'
        },
        {
          key: 'email',
          label: 'Email',
          sortable: true,
          searchable: true,
          width: '250px'
        },
        {
          key: 'status',
          label: 'Status',
          sortable: true,
          searchable: true,
          width: '120px'
        },
        {
          key: 'amount',
          label: 'Amount',
          sortable: true,
          searchable: false,
          width: '120px',
          type: 'number',
          align: 'right'
        },
        {
          key: 'createdAt',
          label: 'Created',
          sortable: true,
          searchable: true,
          width: '150px',
          type: 'date'
        }
      ]
    }
  },

  created() {
    this.generateData()
  },

  methods: {
    /**
     * Generate test data
     */
    generateData() {
      const sizes = {
        small: 100,
        medium: 1000,
        large: 10000,
        xlarge: 50000
      }
      
      const count = sizes[this.dataSize] || 1000
      const statuses = ['Active', 'Inactive', 'Pending', 'Completed', 'Cancelled']
      
      this.testData = Array.from({ length: count }, (_, index) => ({
        id: index + 1,
        name: `User ${index + 1}`,
        email: `user${index + 1}@example.com`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        amount: Math.floor(Math.random() * 10000) + 100,
        createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      }))
      
      this.logEvent('data-generated', `Generated ${count} records`)
    },

    /**
     * Handle performance update
     */
    handlePerformanceUpdate(stats) {
      this.performanceStats = stats
    },

    /**
     * Handle row click
     */
    handleRowClick(payload) {
      this.logEvent('row-click', `Clicked row ${payload.index}: ${payload.item.name}`)
    },

    /**
     * Handle scroll event
     */
    handleScroll(payload) {
      this.logEvent('scroll', `Scroll: ${payload.scrollTop}px, Range: ${payload.startIndex}-${payload.endIndex}, Count: ${payload.visibleCount}`)
    },

    /**
     * Log event
     */
    logEvent(type, data) {
      const event = {
        time: new Date().toLocaleTimeString(),
        type,
        data: typeof data === 'object' ? JSON.stringify(data) : String(data)
      }
      
      this.eventLog.unshift(event)
      
      // Limit log size
      if (this.eventLog.length > this.maxLogEntries) {
        this.eventLog = this.eventLog.slice(0, this.maxLogEntries)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.vue2-datatable-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #374151;
  margin-bottom: 8px;
}

p {
  color: #6b7280;
  font-size: 16px;
  margin-bottom: 30px;
}

.test-controls {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
  }
  
  select {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
  }
}

.performance-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #0369a1;
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
}

.event-log {
  margin-top: 30px;
  
  h3 {
    color: #374151;
    margin-bottom: 16px;
  }
}

.log-entries {
  max-height: 300px;
  overflow-y: auto;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
  font-size: 13px;
  
  &:last-child {
    border-bottom: none;
  }
}

.log-time {
  color: #6b7280;
  font-family: monospace;
  min-width: 80px;
}

.log-type {
  color: #059669;
  font-weight: 500;
  min-width: 100px;
}

.log-data {
  color: #374151;
  flex: 1;
}
</style>
