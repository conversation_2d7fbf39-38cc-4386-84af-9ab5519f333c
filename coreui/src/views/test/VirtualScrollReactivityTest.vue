<template>
  <div class="virtual-scroll-reactivity-test">
    <h1>Virtual Scroll Reactivity Test</h1>
    <p>Testing improved reactivity in virtual scrolling to ensure all records are properly visible.</p>
    
    <div class="test-controls">
      <div class="control-group">
        <label>
          <input v-model="enableVirtualScrolling" type="checkbox" />
          Enable Virtual Scrolling
        </label>
        <label>
          <input v-model="enableDebug" type="checkbox" />
          Enable Debug Mode
        </label>
      </div>
      
      <div class="control-group">
        <label>
          Data Size:
          <select v-model="dataSize" @change="generateData">
            <option value="small">Small (100 rows)</option>
            <option value="medium">Medium (1,000 rows)</option>
            <option value="large">Large (10,000 rows)</option>
            <option value="xlarge">X-Large (50,000 rows)</option>
          </select>
        </label>
        
        <button @click="forceUpdate" class="btn btn-primary">
          Force Update
        </button>
        
        <button @click="scrollToMiddle" class="btn btn-secondary">
          Scroll to Middle
        </button>
        
        <button @click="scrollToEnd" class="btn btn-secondary">
          Scroll to End
        </button>
      </div>
    </div>

    <!-- Debug Information -->
    <div v-if="enableDebug" class="debug-info">
      <h3>Debug Information</h3>
      <div class="debug-grid">
        <div class="debug-item">
          <strong>Total Items:</strong> {{ testData.length }}
        </div>
        <div class="debug-item">
          <strong>Virtual Scrolling:</strong> {{ enableVirtualScrolling ? 'Enabled' : 'Disabled' }}
        </div>
        <div class="debug-item">
          <strong>Visible Items:</strong> {{ visibleItemsCount }}
        </div>
        <div class="debug-item">
          <strong>Reactivity Updates:</strong> {{ reactivityUpdateCount }}
        </div>
      </div>
    </div>

    <!-- Vue2 DataTable -->
    <Vue2DataTable
      ref="dataTable"
      :columns="testColumns"
      :data-source="testData"
      :virtual-scroll-enabled="enableVirtualScrolling"
      :enable-performance-monitoring="enableDebug"
      :show-search="true"
      :show-pagination="false"
      :show-total-bar="true"
      :selectable="true"
      :striped="true"
      :hover="true"
      :row-height="50"
      search-placeholder="Search test data..."
      @virtual-scroll-update="handleVirtualScrollUpdate"
      @scroll="handleScroll"
    />

    <!-- Test Results -->
    <div class="test-results">
      <h3>Test Results</h3>
      <div class="results-grid">
        <div class="result-item">
          <strong>Last Scroll Position:</strong> {{ lastScrollPosition }}px
        </div>
        <div class="result-item">
          <strong>Visible Range:</strong> {{ visibleRange.start }} - {{ visibleRange.end }}
        </div>
        <div class="result-item">
          <strong>Reactivity Working:</strong> 
          <span :class="reactivityWorking ? 'text-success' : 'text-danger'">
            {{ reactivityWorking ? 'YES' : 'NO' }}
          </span>
        </div>
        <div class="result-item">
          <strong>All Records Accessible:</strong>
          <span :class="allRecordsAccessible ? 'text-success' : 'text-danger'">
            {{ allRecordsAccessible ? 'YES' : 'NO' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue2DataTable from '@/components/common/Vue2DataTable'

export default {
  name: 'VirtualScrollReactivityTest',
  
  components: {
    Vue2DataTable
  },

  data() {
    return {
      // Test controls
      enableVirtualScrolling: true,
      enableDebug: true,
      dataSize: 'medium',
      
      // Test data
      testData: [],
      testColumns: [
        {
          key: 'id',
          label: 'ID',
          sortable: true,
          searchable: true,
          width: '80px',
          type: 'number'
        },
        {
          key: 'name',
          label: 'Name',
          sortable: true,
          searchable: true,
          width: '200px'
        },
        {
          key: 'email',
          label: 'Email',
          sortable: true,
          searchable: true,
          width: '250px'
        },
        {
          key: 'status',
          label: 'Status',
          sortable: true,
          searchable: true,
          width: '120px'
        },
        {
          key: 'amount',
          label: 'Amount',
          sortable: true,
          searchable: false,
          width: '120px',
          type: 'number',
          align: 'right'
        }
      ],
      
      // Test tracking
      reactivityUpdateCount: 0,
      lastScrollPosition: 0,
      visibleRange: { start: 0, end: 0 },
      visibleItemsCount: 0,
      maxVisibleRange: { start: 0, end: 0 }
    }
  },

  computed: {
    reactivityWorking() {
      return this.reactivityUpdateCount > 0
    },
    
    allRecordsAccessible() {
      // Check if we've seen a reasonable range of records during scrolling
      const totalRecords = this.testData.length
      const maxEndSeen = this.maxVisibleRange.end
      
      if (totalRecords === 0) return true
      
      // We should be able to access at least 80% of records through scrolling
      return maxEndSeen >= (totalRecords * 0.8)
    }
  },

  created() {
    this.generateData()
  },

  methods: {
    /**
     * Generate test data
     */
    generateData() {
      const sizes = {
        small: 100,
        medium: 1000,
        large: 10000,
        xlarge: 50000
      }
      
      const count = sizes[this.dataSize] || 1000
      const statuses = ['Active', 'Inactive', 'Pending', 'Completed', 'Cancelled']
      
      this.testData = Array.from({ length: count }, (_, index) => ({
        id: index + 1,
        name: `User ${index + 1}`,
        email: `user${index + 1}@example.com`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        amount: Math.floor(Math.random() * 10000) + 100
      }))
      
      // Reset tracking
      this.reactivityUpdateCount = 0
      this.maxVisibleRange = { start: 0, end: 0 }
    },

    /**
     * Handle virtual scroll update
     */
    handleVirtualScrollUpdate(payload) {
      this.reactivityUpdateCount++
      this.visibleRange = {
        start: payload.startIndex,
        end: payload.endIndex
      }
      this.visibleItemsCount = payload.visibleCount
      
      // Track maximum range seen
      if (payload.endIndex > this.maxVisibleRange.end) {
        this.maxVisibleRange.end = payload.endIndex
      }
      if (payload.startIndex < this.maxVisibleRange.start) {
        this.maxVisibleRange.start = payload.startIndex
      }
    },

    /**
     * Handle scroll event
     */
    handleScroll(payload) {
      this.lastScrollPosition = payload.scrollTop
    },

    /**
     * Force update
     */
    forceUpdate() {
      if (this.$refs.dataTable && this.$refs.dataTable.forceVirtualScrollUpdate) {
        this.$refs.dataTable.forceVirtualScrollUpdate()
      }
    },

    /**
     * Scroll to middle
     */
    scrollToMiddle() {
      const middleIndex = Math.floor(this.testData.length / 2)
      if (this.$refs.dataTable && this.$refs.dataTable.scrollToRow) {
        this.$refs.dataTable.scrollToRow(middleIndex)
      }
    },

    /**
     * Scroll to end
     */
    scrollToEnd() {
      const lastIndex = this.testData.length - 1
      if (this.$refs.dataTable && this.$refs.dataTable.scrollToRow) {
        this.$refs.dataTable.scrollToRow(lastIndex)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.virtual-scroll-reactivity-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #374151;
  margin-bottom: 8px;
}

p {
  color: #6b7280;
  font-size: 16px;
  margin-bottom: 30px;
}

.test-controls {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
  }
  
  select {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
  }
  
  .btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    
    &.btn-primary {
      background: #3b82f6;
      color: white;
    }
    
    &.btn-secondary {
      background: #6b7280;
      color: white;
    }
  }
}

.debug-info, .test-results {
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
  
  h3 {
    margin-bottom: 12px;
    color: #0369a1;
  }
}

.debug-grid, .results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.debug-item, .result-item {
  font-size: 14px;
  color: #374151;
}

.text-success {
  color: #059669;
  font-weight: 600;
}

.text-danger {
  color: #dc2626;
  font-weight: 600;
}
</style>
