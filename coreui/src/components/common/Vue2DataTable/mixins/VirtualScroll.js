/**
 * Virtual Scrolling Mixin for Vue2 DataTable - FIXED VERSION
 * Provides virtual scrolling functionality for handling large datasets
 */

import { throttlePerformance, createVirtualListCalculator } from '../utils/performance.js'

export default {
  data() {
    return {
      // Virtual scrolling state
      containerHeight: 600,
      containerWidth: 0,
      scrollTop: 0,
      scrollLeft: 0,

      // Visible range - ensure these are reactive
      startIndex: 0,
      endIndex: 0,
      visibleRowCount: 0,

      // Buffer and optimization
      overscan: 3,

      // Column virtualization
      columnVirtualizationEnabled: false,
      visibleColumnStartIndex: 0,
      visibleColumnEndIndex: 0,
      totalColumnsWidth: 0,

      // Performance tracking
      lastScrollTime: 0,
      scrollDirection: 'down',
      isScrolling: false,
      scrollTimeout: null,

      // Resize observer
      resizeObserver: null,

      // Virtual list calculator
      virtualListCalculator: null,

      // Reactivity management
      _virtualScrollState: {
        revision: 0,
        lastCalculation: null,
        isUpdating: false
      }
    }
  },

  computed: {
    /**
     * Check if virtual scrolling should be enabled
     */
    shouldUseVirtualScrolling() {
      if (this.virtualScrollDisabled) return false

      if (this.virtualScrollEnabled) return true
      if (this.virtualScrollEnabled === false) return false

      const threshold = this.virtualScrollThreshold || 100
      const itemCount = this.filteredItems ? this.filteredItems.length : 0

      return itemCount >= threshold
    },

    /**
     * Check if column virtualization should be enabled
     */
    shouldUseColumnVirtualization() {
      if (!this.shouldUseVirtualScrolling) return false
      
      const threshold = this.columnVirtualizationThreshold || 50
      const columnCount = this.processedColumns ? this.processedColumns.length : 0
      
      return this.columnVirtualizationEnabled || columnCount >= threshold
    },

    /**
     * Get visible items for virtual scrolling - FIXED
     */
    visibleItems() {
      // Force reactivity by accessing the revision
      this._virtualScrollState.revision

      if (!this.shouldUseVirtualScrolling) {
        return this.paginatedItems || this.filteredItems || []
      }

      const items = this.filteredItems || []
      const totalItems = items.length

      if (totalItems === 0) {
        return []
      }

      // Use Math.max to ensure we don't go below 0
      const safeStartIndex = Math.max(0, Math.min(this.startIndex, totalItems - 1))
      const safeEndIndex = Math.max(safeStartIndex, Math.min(this.endIndex, totalItems - 1))

      // CRITICAL FIX: Ensure we slice correctly
      const sliceStart = safeStartIndex
      const sliceEnd = Math.min(safeEndIndex + 1, totalItems)

      const visibleSlice = items.slice(sliceStart, sliceEnd)

      // Debug logging
      if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
        console.log('visibleItems calculation:', {
          totalItems,
          startIndex: this.startIndex,
          endIndex: this.endIndex,
          safeStartIndex,
          safeEndIndex,
          sliceStart,
          sliceEnd,
          resultLength: visibleSlice.length,
          scrollTop: this.scrollTop,
          containerHeight: this.containerHeight,
          revision: this._virtualScrollState.revision
        })
      }

      return visibleSlice
    },

    /**
     * Get visible columns for column virtualization
     */
    visibleColumns() {
      if (!this.shouldUseColumnVirtualization) {
        return this.processedColumns || []
      }

      const columns = this.processedColumns || []
      return columns.slice(this.visibleColumnStartIndex, this.visibleColumnEndIndex + 1)
    },

    /**
     * Calculate top spacer height for virtual scrolling
     */
    topSpacerHeight() {
      this._virtualScrollState.revision // Force reactivity

      if (!this.shouldUseVirtualScrolling) return 0

      const rowHeight = this.rowHeight || 40
      return this.startIndex * rowHeight
    },

    /**
     * Calculate bottom spacer height for virtual scrolling
     */
    bottomSpacerHeight() {
      this._virtualScrollState.revision // Force reactivity

      if (!this.shouldUseVirtualScrolling) return 0

      const totalItems = this.filteredItems ? this.filteredItems.length : 0
      const rowHeight = this.rowHeight || 40
      const remainingItems = Math.max(0, totalItems - (this.endIndex + 1))
      
      return remainingItems * rowHeight
    },

    /**
     * Calculate total virtual height
     */
    totalVirtualHeight() {
      if (!this.shouldUseVirtualScrolling) return 'auto'
      
      const totalItems = this.filteredItems ? this.filteredItems.length : 0
      const rowHeight = this.rowHeight || 40
      return totalItems * rowHeight
    },

    /**
     * Calculate left spacer width for column virtualization
     */
    leftSpacerWidth() {
      if (!this.shouldUseColumnVirtualization) return 0
      
      let width = 0
      const columns = this.processedColumns || []
      for (let i = 0; i < this.visibleColumnStartIndex && i < columns.length; i++) {
        width += this.getColumnWidth(columns[i])
      }
      return width
    },

    /**
     * Calculate right spacer width for column virtualization
     */
    rightSpacerWidth() {
      if (!this.shouldUseColumnVirtualization) return 0
      
      let width = 0
      const columns = this.processedColumns || []
      for (let i = this.visibleColumnEndIndex + 1; i < columns.length; i++) {
        width += this.getColumnWidth(columns[i])
      }
      return width
    }
  },

  mounted() {
    this.initializeVirtualScrolling()
  },

  beforeDestroy() {
    this.cleanupVirtualScrolling()
  },

  methods: {
    /**
     * Initialize virtual scrolling
     */
    initializeVirtualScrolling() {
      this.$nextTick(() => {
        // Create virtual list calculator
        this.virtualListCalculator = createVirtualListCalculator({
          itemHeight: this.rowHeight || 40,
          containerHeight: this.containerHeight || 600,
          bufferSize: this.bufferSize || 5,
          overscan: this.overscan || 3
        })

        this.setupResizeObserver()
        this.updateContainerDimensions()

        this.$nextTick(() => {
          this.calculateVisibleRange()
          this.calculateVisibleColumns()

          if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
            console.log('Virtual scrolling initialized:', {
              containerHeight: this.containerHeight,
              containerWidth: this.containerWidth,
              rowHeight: this.rowHeight,
              itemCount: this.filteredItems ? this.filteredItems.length : 0,
              startIndex: this.startIndex,
              endIndex: this.endIndex
            })
          }
        })
      })
    },

    /**
     * Setup resize observer for container
     */
    setupResizeObserver() {
      if (typeof ResizeObserver !== 'undefined' && this.$refs.tableContainer) {
        this.resizeObserver = new ResizeObserver(
          throttlePerformance(this.handleContainerResize.bind(this), 100)
        )
        this.resizeObserver.observe(this.$refs.tableContainer)
      }
    },

    /**
     * Handle container resize
     */
    handleContainerResize(entries) {
      if (entries && entries.length > 0) {
        const entry = entries[0]
        this.containerHeight = entry.contentRect.height
        this.containerWidth = entry.contentRect.width
        this.calculateVisibleRange()
        this.calculateVisibleColumns()
      }
    },

    /**
     * Update container dimensions
     */
    updateContainerDimensions() {
      if (this.$refs.tableContainer) {
        const rect = this.$refs.tableContainer.getBoundingClientRect()
        const newHeight = rect.height || 600
        const newWidth = rect.width || 800

        if (Math.abs(this.containerHeight - newHeight) > 1 || Math.abs(this.containerWidth - newWidth) > 1) {
          this.containerHeight = newHeight
          this.containerWidth = newWidth

          if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
            console.log('Container dimensions updated:', {
              height: this.containerHeight,
              width: this.containerWidth
            })
          }
        }
      } else {
        if (this.containerHeight === 0) this.containerHeight = 600
        if (this.containerWidth === 0) this.containerWidth = 800
      }
    },

    /**
     * Handle scroll event - FIXED
     */
    handleScroll(event) {
      if (!this.shouldUseVirtualScrolling) return

      const target = event.target
      const newScrollTop = target.scrollTop
      const newScrollLeft = target.scrollLeft

      // Only process if scroll position actually changed
      if (Math.abs(newScrollTop - this.scrollTop) < 1 && Math.abs(newScrollLeft - this.scrollLeft) < 1) {
        return
      }

      this.scrollDirection = newScrollTop > this.scrollTop ? 'down' : 'up'
      this.scrollTop = newScrollTop
      this.scrollLeft = newScrollLeft
      this.lastScrollTime = Date.now()
      this.isScrolling = true

      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
      }

      // CRITICAL FIX: Calculate visible range immediately, not in throttled handler
      this.calculateVisibleRange()
      this.calculateVisibleColumns()

      // Use throttled handler for events only
      if (!this._throttledScrollHandler) {
        this._throttledScrollHandler = throttlePerformance(this.emitScrollEvent.bind(this), 16)
      }
      this._throttledScrollHandler()

      this.scrollTimeout = setTimeout(() => {
        this.isScrolling = false
        this.onScrollEnd()
      }, 150)
    },

    /**
     * Emit scroll event (throttled)
     */
    emitScrollEvent() {
      this.$emit('scroll', {
        scrollTop: this.scrollTop,
        scrollLeft: this.scrollLeft,
        direction: this.scrollDirection,
        startIndex: this.startIndex,
        endIndex: this.endIndex,
        visibleCount: this.visibleRowCount,
        isScrolling: this.isScrolling,
        revision: this._virtualScrollState.revision
      })
    },

    /**
     * Handle scroll end
     */
    onScrollEnd() {
      this.$emit('scroll-end', {
        scrollTop: this.scrollTop,
        scrollLeft: this.scrollLeft
      })
    },

    /**
     * Calculate visible row range - FIXED
     */
    calculateVisibleRange() {
      if (this._virtualScrollState.isUpdating) {
        return
      }

      this._virtualScrollState.isUpdating = true

      try {
        if (!this.shouldUseVirtualScrolling) {
          const itemCount = this.filteredItems ? this.filteredItems.length : 0
          this._updateIndices(0, Math.max(0, itemCount - 1), itemCount)
          return
        }

        const itemCount = this.filteredItems ? this.filteredItems.length : 0
        if (itemCount === 0) {
          this._updateIndices(0, 0, 0)
          return
        }

        this.updateContainerDimensions()

        const rowHeight = this.rowHeight || 40
        const containerHeight = this.containerHeight > 0 ? this.containerHeight : 600
        const overscan = this.overscan || 3

        // CRITICAL FIX: Proper calculation of visible range
        const visibleStartIndex = Math.floor(this.scrollTop / rowHeight)
        const visibleEndIndex = Math.min(
          itemCount - 1,
          Math.ceil((this.scrollTop + containerHeight) / rowHeight) - 1
        )

        // Apply overscan buffer
        const bufferedStartIndex = Math.max(0, visibleStartIndex - overscan)
        const bufferedEndIndex = Math.min(itemCount - 1, visibleEndIndex + overscan)

        const visibleCount = bufferedEndIndex - bufferedStartIndex + 1

        this._updateIndices(bufferedStartIndex, bufferedEndIndex, visibleCount)

        if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
          console.log('Virtual Scroll Range Calculated:', {
            scrollTop: this.scrollTop,
            rowHeight,
            containerHeight,
            itemCount,
            visibleStartIndex,
            visibleEndIndex,
            bufferedStartIndex,
            bufferedEndIndex,
            visibleCount,
            revision: this._virtualScrollState.revision
          })
        }
      } finally {
        this._virtualScrollState.isUpdating = false
      }
    },

    /**
     * Efficiently update virtual scroll indices - FIXED
     */
    _updateIndices(newStartIndex, newEndIndex, newVisibleCount) {
      const hasChanged =
        this.startIndex !== newStartIndex ||
        this.endIndex !== newEndIndex ||
        this.visibleRowCount !== newVisibleCount

      if (hasChanged) {
        this.startIndex = newStartIndex
        this.endIndex = newEndIndex
        this.visibleRowCount = newVisibleCount

        // Increment revision to trigger reactivity
        this._virtualScrollState.revision++
        this._virtualScrollState.lastCalculation = Date.now()

        // Emit update event
        this.$emit('virtual-scroll-update', {
          startIndex: this.startIndex,
          endIndex: this.endIndex,
          visibleCount: this.visibleRowCount,
          revision: this._virtualScrollState.revision
        })

        if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
          console.log('Indices updated:', {
            startIndex: this.startIndex,
            endIndex: this.endIndex,
            visibleCount: this.visibleRowCount,
            revision: this._virtualScrollState.revision
          })
        }
      }
    },

    /**
     * Calculate visible column range
     */
    calculateVisibleColumns() {
      if (!this.shouldUseColumnVirtualization) {
        this.visibleColumnStartIndex = 0
        this.visibleColumnEndIndex = this.processedColumns ? this.processedColumns.length - 1 : 0
        return
      }

      const columns = this.processedColumns || []
      if (columns.length === 0) {
        this.visibleColumnStartIndex = 0
        this.visibleColumnEndIndex = 0
        return
      }

      let currentWidth = 0
      let startIndex = 0
      let endIndex = 0

      // Find start index
      for (let i = 0; i < columns.length; i++) {
        const columnWidth = this.getColumnWidth(columns[i])
        if (currentWidth + columnWidth > this.scrollLeft) {
          startIndex = Math.max(0, i - 1)
          break
        }
        currentWidth += columnWidth
      }

      // Find end index
      currentWidth = 0
      for (let i = startIndex; i < columns.length; i++) {
        const columnWidth = this.getColumnWidth(columns[i])
        currentWidth += columnWidth
        if (currentWidth >= this.containerWidth + 200) {
          endIndex = i
          break
        }
        endIndex = i
      }

      this.visibleColumnStartIndex = startIndex
      this.visibleColumnEndIndex = Math.min(endIndex, columns.length - 1)
    },

    /**
     * Get column width
     */
    getColumnWidth(column) {
      if (column.width) {
        if (typeof column.width === 'number') {
          return column.width
        }
        if (typeof column.width === 'string') {
          const match = column.width.match(/(\d+)/)
          return match ? parseInt(match[1]) : this.columnWidth || 150
        }
      }
      return this.columnWidth || 150
    },

    /**
     * Scroll to specific row
     */
    scrollToRow(index) {
      if (!this.shouldUseVirtualScrolling) return

      const targetScrollTop = index * (this.rowHeight || 40)
      if (this.$refs.tableContainer) {
        this.$refs.tableContainer.scrollTop = targetScrollTop
      }
    },

    /**
     * Scroll to specific column
     */
    scrollToColumn(index) {
      if (!this.shouldUseColumnVirtualization) return

      let targetScrollLeft = 0
      const columns = this.processedColumns || []
      
      for (let i = 0; i < index && i < columns.length; i++) {
        targetScrollLeft += this.getColumnWidth(columns[i])
      }

      if (this.$refs.tableContainer) {
        this.$refs.tableContainer.scrollLeft = targetScrollLeft
      }
    },

    /**
     * Get row offset for virtual positioning
     */
    getRowOffset(index) {
      return (this.startIndex + index) * (this.rowHeight || 40)
    },

    /**
     * Check if row is visible
     */
    isRowVisible(index) {
      return index >= this.startIndex && index <= this.endIndex
    },

    /**
     * Check if column is visible
     */
    isColumnVisible(index) {
      return index >= this.visibleColumnStartIndex && index <= this.visibleColumnEndIndex
    },

    /**
     * Cleanup virtual scrolling
     */
    cleanupVirtualScrolling() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      }

      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
        this.scrollTimeout = null
      }

      if (this._scrollUpdateTimeout) {
        clearTimeout(this._scrollUpdateTimeout)
        this._scrollUpdateTimeout = null
      }

      this._throttledScrollHandler = null
      this.resetVirtualScrollState()
    },

    /**
     * Force recalculation of virtual scrolling
     */
    recalculateVirtualScrolling() {
      this.$nextTick(() => {
        this.updateContainerDimensions()
        this.calculateVisibleRange()
        this.calculateVisibleColumns()
      })
    },

    /**
     * Force reactivity update for virtual scrolling
     */
    forceVirtualScrollUpdate() {
      this._virtualScrollState.revision++
      this.$nextTick(() => {
        if (this.shouldUseVirtualScrolling) {
          this.calculateVisibleRange()
        }
      })
    },

    /**
     * Reset virtual scroll state
     */
    resetVirtualScrollState() {
      this._virtualScrollState = {
        revision: 0,
        lastCalculation: null,
        isUpdating: false
      }

      this.startIndex = 0
      this.endIndex = 0
      this.visibleRowCount = 0
      this.scrollTop = 0
      this.scrollLeft = 0
    }
  },

  watch: {
    filteredItems: {
      handler(newItems, oldItems) {
        if (!oldItems || newItems.length !== oldItems.length || newItems !== oldItems) {
          this.$nextTick(() => {
            this.calculateVisibleRange()

            if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
              console.log('filteredItems changed:', {
                oldLength: oldItems ? oldItems.length : 0,
                newLength: newItems ? newItems.length : 0,
                startIndex: this.startIndex,
                endIndex: this.endIndex,
                revision: this._virtualScrollState.revision
              })
            }
          })
        }
      },
      immediate: true
    },

    processedColumns: {
      handler() {
        this.$nextTick(() => {
          this.calculateVisibleColumns()
        })
      },
      immediate: true
    },

    rowHeight() {
      if (this.virtualListCalculator) {
        this.virtualListCalculator = createVirtualListCalculator({
          itemHeight: this.rowHeight || 40,
          containerHeight: this.containerHeight || 600,
          bufferSize: this.bufferSize || 5,
          overscan: this.overscan || 3
        })
      }
      this.forceVirtualScrollUpdate()
    },

    columnWidth() {
      this.calculateVisibleColumns()
    },

    shouldUseVirtualScrolling: {
      handler(newValue, oldValue) {
        if (newValue && newValue !== oldValue) {
          this.$nextTick(() => {
            this.initializeVirtualScrolling()
          })
        } else if (!newValue && oldValue) {
          const itemCount = this.filteredItems ? this.filteredItems.length : 0
          this._updateIndices(0, Math.max(0, itemCount - 1), itemCount)
        }
      },
      immediate: true
    },

    containerHeight(newHeight, oldHeight) {
      if (Math.abs(newHeight - oldHeight) > 1 && this.shouldUseVirtualScrolling) {
        this.$nextTick(() => {
          this.calculateVisibleRange()
        })
      }
    }

    // REMOVED the scrollTop watcher as it was causing issues with the scroll handler
  }
}